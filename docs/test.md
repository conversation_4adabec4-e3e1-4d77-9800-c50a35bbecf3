# 测试脚本

## 渠道商品映射关系

```bash
curl -i -X POST -H "Content-Type: application/json" 'http://127.0.0.1:8550/proxy/online.waiter-query.service.mailsaas/waiter-query/api/query/632c1fb83450113db4643a9a/kefu-ai-agents' -d{}

curl -i -X POST -H "Content-Type: application/json" 'http://127.0.0.1:8550/proxy/test4dp.yanxuan-ianus.service.mailsaas/waiter-query/api/query/63200bed6659de37c5a0b3b7/kefu-ai-agents' -d {}
```

## 问答消息意图总结

```bash
curl -i -X POST "http://localhost:8080/qa/message/intent" -H "Content-Type: application/json" -d '{
  "session_id": "session_123",
  "session_context": [
    {
      "content_type": 1,
      "source": 1,
      "content": "我想退货"
    }
  ],
  "current_message": {
    "content_type": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```

```bash
curl -i -X POST "http://localhost:8080/qa/message/intent" -H "Content-Type: application/json" -d '{
  "sessionId": "session_123",
  "sessionContext": [
    {
      "contentType": 1,
      "source": 1,
      "content": "我想退货"
    }
  ],
  "currentMessage": {
    "contentType": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```

```bash
curl -i -X POST "http://127.0.0.1:8550/proxy/test.kefu-ai-agents.service.mailsaas/qa/message/intent" -H "Content-Type: application/json" -d '{
  "sessionId": "session_123",
  "sessionContext": [
    {
      "contentType": 1,
      "source": 1,
      "content": "我想退货"
    }
  ],
  "currentMessage": {
    "contentType": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```bash
curl -i -X POST "http://127.0.0.1:8550/proxy/online.kefu-ai-agents.service.mailsaas/qa/message/intent" -H "Content-Type: application/json" -d '{
  "sessionId": "session_123",
  "sessionContext": [
    {
      "contentType": 1,
      "source": 1,
      "content": "我想退货"
    }
  ],
  "currentMessage": {
    "contentType": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```

```bash
curl -i -X POST "http://localhost:8080/qa/message/intent" -H "Content-Type: application/json" -d '{
  "session_id": "session_123",
  "session_context": [
    {
      "content_type": 1,
      "source": 1,
      "content": "我想退货"
    },
    {
      "content_type": 4,
      "source": 1,
      "content": "{\"itemList\":[{\"skuId\":\"100007926902\"}]}"
    }
  ],
  "current_message": {
    "content_type": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```

```bash
curl -i -X POST "http://localhost:8080/qa/message/intent" -H "Content-Type: application/json" -d '{
  "sessionId": "session_123",
  "sessionContext": [
    {
      "contentType": 1,
      "source": 1,
      "content": "我想退货"
    },
    {
      "contentType": 4,
      "source": 1,
      "content": "{\"itemList\":[{\"skuId\":\"100007926902\"}]}"
    }
  ],
  "currentMessage": {
    "contentType": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```

```bash
curl -i -X POST "http://localhost:8080/qa/message/intent" -H "Content-Type: application/json" -d '{
  "sessionId": "session_123",
  "sessionContext": [
  {
    "contentType": 1,
    "source": 3,
    "content": "小选很幸运能遇见您，请问有什么可以帮您~\n以下是人工客服接待，人工小选会帮您妥善处理您的需求，亲亲放心哦(#^.^#)"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "现在都降价50块了"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，小选来啦~不管遇到什么问题，小选都会竭尽全力帮您的哦~"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，备注只有赠品盖毯，没有返现50哈"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "？"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "我那时候买的"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "主播说了"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "找客服返50"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "你这样我要退货了"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "我现在买还能省50"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "这一来一去 我亏100"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，没有备注返现50哈"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "实在抱歉，给您添麻烦了，亲亲"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "主播在直播间自己说的"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "那退了吧"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "好的，亲亲"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "商品签收后7天内未影响商品二次销售，可申请退货退款哦"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "我不拆 直接整椅让快递员拿走了"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，不拆放不了箱子啊"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "那和我有什么关系呢"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "你自己拆啊"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "主播自己说的返50 现在不算数了"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "然后现在还有优惠券"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "50"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "我为什么要亏呢"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，辛苦您提供一下主播说返现50的截图或视频可以吗，小选为您核实处理"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "自己让快递员给你拆吧"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "没能让您满意 实在是抱歉呢亲"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "他直播自己说的 我还录下来啊"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "是的，亲亲"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "需要提供凭证可以帮您反馈哈"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "不用了 退了就行"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "实在抱歉，给您添麻烦了"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "好的，亲亲"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "让快递员自己来拿"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，辛苦申请一下售后，售后审核时效24H,退回仓库签收后会在48h内质检，核实无误后售后会在1-3天内给您处理退款的哈，请放心哦"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "反正我不会帮你拆下来"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，选择上门取件即可"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，不拆快递员应该不会收的哦"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "给您的购物造成麻烦，是我们没有做好，真诚地和您说声抱歉了"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "不收和我没关系啊"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "你自己找人上门拆吧"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "您的心情我非常理解，的的确确给您带来了极差的体验，我确实能理解您的无奈，希望您能包涵哦，不要让这些事情影响了您的心情"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，上门拆需要拆卸费的哦"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "反正我不会出钱"
  },
  {
    "contentType": 1,
    "source": 1,
    "content": "你这运费险已经包了运费了"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "亲亲，很抱歉给您不好的购物体验了，既然现在是我接到您的问题，说明咱是非常有缘分的"
  },
  {
    "contentType": 1,
    "source": 2,
    "content": "平台开设这个窗口就是为了给消费者处理问题的，所以您的问题我们这边也会认真的来对待的"
  }
],
  "currentMessage": {
    "contentType": 1,
    "source": 1,
    "content": "这椅子我要不要无所谓 自己主播说的话 不承认 我就不想要了啊"
  }
}'
```


## 问答消息意图总结

```bash
curl -i -X POST "http://localhost:8080/mps/consume" -H "Content-Type: application/json" -d '[{"product":"","topic":"","messageId":"","payload":""}]'
```