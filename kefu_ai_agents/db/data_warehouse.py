import os
import waiterdb
import pandas as pd
from typing import Optional, List
from contextlib import contextmanager
from dataclasses import dataclass
from kefu_ai_agents.utils.loguru_config import get_logger

logger = get_logger(__name__)


@dataclass
class DataWarehouseConfig:
    """数据仓库配置类"""

    url: str
    user: str
    password: str
    source_type: str = "spark"
    autocommit: bool = True

    @classmethod
    def from_env(cls) -> "DataWarehouseConfig":
        """从环境变量创建配置"""
        return cls(
            url=os.getenv("DW_URL", "http://holmes-waiter.hz.infra.mail"),
            user=os.getenv("DW_USER", "zhuxulv"),
            password=os.getenv("DW_PASSWORD", "fpecz0vc"),
            source_type=os.getenv("DW_SOURCE_TYPE", "spark"),
            autocommit=os.getenv("DW_AUTOCOMMIT", "true").lower() == "true",
        )


class DataWarehouseReader:
    """数据仓库读取器"""

    def __init__(self, config: Optional[DataWarehouseConfig] = None):
        self.config = config or DataWarehouseConfig.from_env()

    @contextmanager
    def _get_connection(self):
        """数据仓库连接上下文管理器"""
        connection = None
        cursor = None
        try:
            logger.info("正在连接数据仓库...")
            connection = waiterdb.connect(
                url=self.config.url,
                user=self.config.user,
                password=self.config.password,
                sourceType=self.config.source_type,
                autocommit=self.config.autocommit,
            )
            cursor = connection.cursor()
            logger.info("数据仓库连接成功")
            yield connection, cursor
        except Exception as e:
            logger.error("数据仓库连接失败: {}", e)
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
            logger.info("数据仓库连接已关闭")

    def _validate_sql(self, sql: str) -> None:
        """验证SQL语句"""
        if not sql or not sql.strip():
            raise ValueError("SQL语句不能为空")

        # 基本的SQL注入防护
        dangerous_keywords = ["drop", "delete", "truncate", "alter", "create"]
        sql_lower = sql.lower().strip()
        for keyword in dangerous_keywords:
            if keyword in sql_lower and not sql_lower.startswith("select"):
                logger.warning("检测到潜在危险SQL操作: {}", keyword)

    def get_data(
        self,
        sql: str,
        columns: Optional[List[str]] = None,
        chunk_size: Optional[int] = None,
    ) -> pd.DataFrame:
        """
        执行SQL查询并返回DataFrame

        Args:
            sql: SQL查询语句
            columns: 列名列表，如果为None则自动获取
            chunk_size: 分块大小，用于大数据集处理

        Returns:
            pandas.DataFrame: 查询结果

        Raises:
            ValueError: SQL语句无效
            Exception: 数据仓库操作异常
        """
        self._validate_sql(sql)

        try:
            with self._get_connection() as (connection, cursor):
                logger.info("执行SQL查询: {}...", sql[:100])
                cursor.execute(sql)

                # 获取列名
                if columns is None:
                    columns = [
                        column["columnName"] for column in cursor._signature["columns"]
                    ]
                    logger.debug("自动获取列名: {}", columns)

                # 获取数据
                if chunk_size:
                    data_chunks = []
                    while True:
                        chunk = cursor.fetchmany(chunk_size)
                        if not chunk:
                            break
                        data_chunks.extend(chunk)
                    data = data_chunks
                else:
                    data = cursor.fetchall()

                # 创建DataFrame
                df = pd.DataFrame(data, columns=columns)
                logger.info("查询完成，获取 {} 行数据", len(df))
                return df

        except Exception as e:
            logger.error("查询执行失败: {}", e)
            raise

    def test_connection(self) -> bool:
        """测试数据仓库连接"""
        try:
            with self._get_connection() as (connection, cursor):
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                logger.info("数据仓库连接测试成功")
                return bool(result)
        except Exception as e:
            logger.error("数据仓库连接测试失败: {}", e)
            return False


_dw_reader = DataWarehouseReader()


def get_reader() -> DataWarehouseReader:
    return _dw_reader


if __name__ == "__main__":
    # 创建读取器实例
    reader = get_reader()

    # 测试连接
    if reader.test_connection():
        try:
            # 执行查询
            sql = (
                "SELECT * FROM dm.dm_yx_damai_comment_ai_analysis_extraction_p LIMIT 10"
            )
            data = reader.get_data(sql)
            print(f"数据形状: {data.shape}")
            print(data.head())
        except Exception as e:
            logger.error("查询执行失败: {}", e)
    else:
        logger.error("数据仓库连接失败，无法执行查询")
