import os
import json
import threading
from typing import Any, Dict, List, Optional
from contextlib import contextmanager

from redis.cluster import RedisCluster, ClusterNode
from redis.exceptions import (
    ConnectionError,
    TimeoutError,
    RedisError,
    ClusterDownError,
)

from kefu_ai_agents.utils.loguru_config import get_logger
from dotenv import load_dotenv


# 加载环境变量
load_dotenv()

# 初始化日志
logger = get_logger(__name__)


class RedisConfig:
    """Redis配置类"""

    def __init__(self):
        """从环境变量初始化配置"""
        # 必需配置
        nodes_str = os.getenv("REDIS_CLUSTER_NODES", "")
        if not nodes_str:
            raise ValueError("REDIS_CLUSTER_NODES environment variable is required")

        # 解析集群节点
        self.cluster_nodes = self._parse_nodes(nodes_str)

        # 可选配置，使用默认值
        self.password = os.getenv("REDIS_PASSWORD")
        self.prefix = os.getenv("REDIS_PREFIX", "")
        self.socket_timeout = 5.0
        self.socket_connect_timeout = 5.0
        self.retry_on_timeout = True
        self.health_check_interval = 30
        self.max_connections = 50

    def _parse_nodes(self, nodes_str: str) -> List[ClusterNode]:
        """解析节点字符串为 ClusterNode 列表"""
        nodes = []
        for node in nodes_str.split(","):
            node = node.strip()
            if ":" in node:
                host, port = node.split(":", 1)
                nodes.append(ClusterNode(host=host.strip(), port=int(port.strip())))
            else:
                nodes.append(ClusterNode(host=node.strip(), port=6379))
        return nodes


class RedisClientError(Exception):
    """Redis客户端异常"""

    pass


class RedisClient:
    """Redis集群客户端单例类

    线程安全的单例实现，支持Redis集群模式，提供基本的缓存操作
    """

    _instance: Optional["RedisClient"] = None
    _lock = threading.Lock()

    def __new__(cls) -> "RedisClient":
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    instance = super().__new__(cls)
                    instance._initialize()
                    cls._instance = instance
        return cls._instance

    def _initialize(self) -> None:
        """初始化方法，只会被调用一次"""
        self._client: Optional[RedisCluster] = None
        self._config: Optional[RedisConfig] = None
        self._is_initialized: bool = False
        self._init_redis_client()

    def _init_redis_client(self) -> None:
        """初始化Redis客户端"""
        try:
            self._config = RedisConfig()

            # 创建Redis集群客户端
            self._client = RedisCluster(
                startup_nodes=self._config.cluster_nodes,
                password=self._config.password,
                socket_timeout=self._config.socket_timeout,
                socket_connect_timeout=self._config.socket_connect_timeout,
                retry_on_timeout=self._config.retry_on_timeout,
                health_check_interval=self._config.health_check_interval,
                max_connections=self._config.max_connections,
                decode_responses=True,  # 自动解码响应为字符串
                skip_full_coverage_check=True,  # 跳过完整覆盖检查
            )

            # 测试连接
            self._client.ping()
            self._is_initialized = True

            logger.info(
                "[RedisClient] 初始化成功 - nodes: %d个, prefix: %s",
                len(self._config.cluster_nodes),
                self._config.prefix,
            )

        except Exception as e:
            error_msg = f"Redis客户端初始化失败: {str(e)}"
            logger.error(error_msg)
            raise RedisClientError(error_msg) from e

    @property
    def client(self) -> RedisCluster:
        """获取Redis客户端实例"""
        if not self._is_initialized or self._client is None:
            raise RedisClientError("Redis客户端未初始化")
        return self._client

    def _get_key(self, key: str) -> str:
        """获取带前缀的键名"""
        if not key:
            raise ValueError("键名不能为空")
        if self._config and self._config.prefix:
            return f"{self._config.prefix}{key}"
        return key

    @contextmanager
    def error_context(self, operation: str):
        """错误上下文管理器"""
        try:
            yield
        except (ConnectionError, TimeoutError, ClusterDownError) as e:
            error_msg = f"Redis连接错误 - {operation}: {str(e)}"
            logger.error(error_msg)
            raise RedisClientError(error_msg) from e
        except RedisError as e:
            error_msg = f"Redis操作错误 - {operation}: {str(e)}"
            logger.error(error_msg)
            raise RedisClientError(error_msg) from e
        except Exception as e:
            error_msg = f"未知错误 - {operation}: {str(e)}"
            logger.error(error_msg)
            raise RedisClientError(error_msg) from e

    def ping(self) -> bool:
        """测试Redis连接"""
        try:
            with self.error_context("ping"):
                result = self.client.ping()
                logger.debug("[RedisClient] ping成功")
                return bool(result)
        except RedisClientError:
            return False

    def set(
        self, key: str, value: Any, ex: Optional[int] = None, nx: bool = False
    ) -> bool:
        """设置键值

        Args:
            key: 键名
            value: 值，会自动序列化为JSON
            ex: 过期时间（秒）
            nx: 仅当键不存在时设置

        Returns:
            是否设置成功
        """
        with self.error_context(f"set key={key}"):
            full_key = self._get_key(key)

            # 序列化值
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            elif isinstance(value, (int, float, bool)):
                serialized_value = str(value)
            else:
                serialized_value = str(value)

            result = self.client.set(full_key, serialized_value, ex=ex, nx=nx)
            logger.debug("[RedisClient] set成功 - key: %s, ex: %s", full_key, ex)
            return bool(result)

    def get(self, key: str, default: Any = None) -> Any:
        """获取键值

        Args:
            key: 键名
            default: 默认值

        Returns:
            键对应的值，如果不存在返回默认值
        """
        with self.error_context(f"get key={key}"):
            full_key = self._get_key(key)
            result = self.client.get(full_key)

            if result is None:
                logger.debug("[RedisClient] get未找到 - key: %s", full_key)
                return default

            # 确保结果是字符串类型
            str_result = str(result) if result is not None else ""

            # 尝试反序列化JSON
            try:
                return json.loads(str_result)
            except (json.JSONDecodeError, TypeError):
                # 如果不是JSON，直接返回字符串
                logger.debug("[RedisClient] get成功 - key: %s", full_key)
                return str_result

    def delete(self, *keys: str) -> int:
        """删除键

        Args:
            keys: 要删除的键名列表

        Returns:
            实际删除的键数量
        """
        if not keys:
            return 0

        with self.error_context(f"delete keys={keys}"):
            full_keys = [self._get_key(key) for key in keys]
            result = self.client.delete(*full_keys)
            try:
                # 安全的类型转换
                count = int(result)  # type: ignore
            except (ValueError, TypeError, AttributeError):
                count = 0
            logger.debug(
                "[RedisClient] delete成功 - keys: %s, count: %d", full_keys, count
            )
            return count

    def exists(self, key: str) -> bool:
        """检查键是否存在

        Args:
            key: 键名

        Returns:
            键是否存在
        """
        with self.error_context(f"exists key={key}"):
            full_key = self._get_key(key)
            result = bool(self.client.exists(full_key))
            logger.debug("[RedisClient] exists - key: %s, result: %s", full_key, result)
            return result

    def expire(self, key: str, time: int) -> bool:
        """设置键的过期时间

        Args:
            key: 键名
            time: 过期时间（秒）

        Returns:
            是否设置成功
        """
        with self.error_context(f"expire key={key}"):
            full_key = self._get_key(key)
            result = bool(self.client.expire(full_key, time))
            logger.debug(
                "[RedisClient] expire - key: %s, time: %d, result: %s",
                full_key,
                time,
                result,
            )
            return result

    def ttl(self, key: str) -> int:
        """获取键的剩余生存时间

        Args:
            key: 键名

        Returns:
            剩余生存时间（秒），-1表示永不过期，-2表示键不存在
        """
        with self.error_context(f"ttl key={key}"):
            full_key = self._get_key(key)
            result = self.client.ttl(full_key)
            ttl_value = int(result)  # type: ignore
            logger.debug("[RedisClient] ttl - key: %s, result: %d", full_key, ttl_value)
            return ttl_value

    def hset(self, name: str, mapping: Dict[str, Any]) -> int:
        """设置哈希表字段

        Args:
            name: 哈希表名
            mapping: 字段映射字典

        Returns:
            新增字段数量
        """
        with self.error_context(f"hset name={name}"):
            full_name = self._get_key(name)

            # 序列化值
            serialized_mapping = {}
            for key, value in mapping.items():
                if isinstance(value, (dict, list)):
                    serialized_mapping[key] = json.dumps(value, ensure_ascii=False)
                else:
                    serialized_mapping[key] = str(value)

            result = self.client.hset(full_name, mapping=serialized_mapping)
            count = int(result)  # type: ignore
            logger.debug(
                "[RedisClient] hset成功 - name: %s, count: %d", full_name, count
            )
            return count

    def hget(self, name: str, key: str, default: Any = None) -> Any:
        """获取哈希表字段值

        Args:
            name: 哈希表名
            key: 字段名
            default: 默认值

        Returns:
            字段值
        """
        with self.error_context(f"hget name={name}, key={key}"):
            full_name = self._get_key(name)
            result = self.client.hget(full_name, key)

            if result is None:
                logger.debug(
                    "[RedisClient] hget未找到 - name: %s, key: %s", full_name, key
                )
                return default

            # 确保结果是字符串类型
            str_result = str(result) if result is not None else ""

            # 尝试反序列化JSON
            try:
                return json.loads(str_result)
            except (json.JSONDecodeError, TypeError):
                logger.debug(
                    "[RedisClient] hget成功 - name: %s, key: %s", full_name, key
                )
                return str_result

    def hgetall(self, name: str) -> Dict[str, Any]:
        """获取哈希表所有字段

        Args:
            name: 哈希表名

        Returns:
            所有字段的字典
        """
        with self.error_context(f"hgetall name={name}"):
            full_name = self._get_key(name)
            result = self.client.hgetall(full_name)

            if not result:
                logger.debug("[RedisClient] hgetall为空 - name: %s", full_name)
                return {}

            # 反序列化所有值
            deserialized_result = {}
            # 使用 type: ignore 来处理类型检查问题
            for key, value in result.items():  # type: ignore
                str_value = str(value) if value is not None else ""
                try:
                    deserialized_result[key] = json.loads(str_value)
                except (json.JSONDecodeError, TypeError):
                    deserialized_result[key] = str_value

            logger.debug(
                "[RedisClient] hgetall成功 - name: %s, count: %d",
                full_name,
                len(deserialized_result),
            )
            return deserialized_result

    def hdel(self, name: str, *keys: str) -> int:
        """删除哈希表字段

        Args:
            name: 哈希表名
            keys: 要删除的字段名列表

        Returns:
            实际删除的字段数量
        """
        if not keys:
            return 0

        with self.error_context(f"hdel name={name}, keys={keys}"):
            full_name = self._get_key(name)
            result = self.client.hdel(full_name, *keys)
            count = int(result)  # type: ignore
            logger.debug(
                "[RedisClient] hdel成功 - name: %s, keys: %s, count: %d",
                full_name,
                keys,
                count,
            )
            return count

    def incr(self, key: str, amount: int = 1) -> int:
        """递增键值

        Args:
            key: 键名
            amount: 递增量

        Returns:
            递增后的值
        """
        with self.error_context(f"incr key={key}"):
            full_key = self._get_key(key)
            result = self.client.incr(full_key, amount)
            value = int(result)  # type: ignore
            logger.debug(
                "[RedisClient] incr成功 - key: %s, amount: %d, value: %d",
                full_key,
                amount,
                value,
            )
            return value

    def decr(self, key: str, amount: int = 1) -> int:
        """递减键值

        Args:
            key: 键名
            amount: 递减量

        Returns:
            递减后的值
        """
        with self.error_context(f"decr key={key}"):
            full_key = self._get_key(key)
            result = self.client.decr(full_key, amount)
            value = int(result)  # type: ignore
            logger.debug(
                "[RedisClient] decr成功 - key: %s, amount: %d, value: %d",
                full_key,
                amount,
                value,
            )
            return value

    def scan(self, cursor: int = 0, match: Optional[str] = None, count: Optional[int] = None) -> tuple[int, List[str]]:
        """扫描键名

        Args:
            cursor: 游标位置，0表示开始新的扫描
            match: 匹配模式，支持通配符 * 和 ?
            count: 每次扫描返回的键数量提示（实际数量可能不同）

        Returns:
            元组 (新游标, 键名列表)，新游标为0表示扫描完成
        """
        with self.error_context(f"scan cursor={cursor}"):
            # 构建扫描参数
            scan_kwargs = {}
            if match is not None:
                # 如果有前缀，需要在匹配模式中包含前缀
                if self._config and self._config.prefix:
                    if match.startswith(self._config.prefix):
                        scan_kwargs['match'] = match
                    else:
                        scan_kwargs['match'] = f"{self._config.prefix}{match}"
                else:
                    scan_kwargs['match'] = match
            if count is not None:
                scan_kwargs['count'] = count

            # 执行扫描
            result = self.client.scan(cursor, **scan_kwargs)
            new_cursor = int(result[0])  # type: ignore
            keys = list(result[1])  # type: ignore

            # 如果有前缀，需要从返回的键名中移除前缀
            if self._config and self._config.prefix:
                prefix_len = len(self._config.prefix)
                keys = [key[prefix_len:] if key.startswith(self._config.prefix) else key for key in keys]

            logger.debug(
                "[RedisClient] scan成功 - cursor: %d, new_cursor: %d, count: %d",
                cursor,
                new_cursor,
                len(keys),
            )
            return new_cursor, keys

    def scan_iter(self, match: Optional[str] = None, count: Optional[int] = None):
        """扫描键名迭代器

        Args:
            match: 匹配模式，支持通配符 * 和 ?
            count: 每次扫描返回的键数量提示（实际数量可能不同）

        Yields:
            键名
        """
        with self.error_context("scan_iter"):
            # 构建扫描参数
            scan_kwargs = {}
            if match is not None:
                # 如果有前缀，需要在匹配模式中包含前缀
                if self._config and self._config.prefix:
                    if match.startswith(self._config.prefix):
                        scan_kwargs['match'] = match
                    else:
                        scan_kwargs['match'] = f"{self._config.prefix}{match}"
                else:
                    scan_kwargs['match'] = match
            if count is not None:
                scan_kwargs['count'] = count

            # 使用Redis客户端的scan_iter方法
            for key in self.client.scan_iter(**scan_kwargs):
                # 如果有前缀，需要从返回的键名中移除前缀
                if self._config and self._config.prefix:
                    if key.startswith(self._config.prefix):
                        yield key[len(self._config.prefix):]
                    else:
                        yield key
                else:
                    yield key


# 全局访问函数
_redis_client: Optional[RedisClient] = None


def get_redis_client() -> RedisClient:
    """获取Redis客户端单例实例

    Returns:
        RedisClient实例
    """
    global _redis_client
    if _redis_client is None:
        _redis_client = RedisClient()
    return _redis_client


def test_connection() -> bool:
    """测试Redis连接

    Returns:
        连接是否成功
    """
    try:
        client = get_redis_client()
        return client.ping()
    except Exception as e:
        logger.error("Redis连接测试失败: %s", e)
        return False


# 便捷函数
def cache_set(key: str, value: Any, ex: Optional[int] = None) -> bool:
    """缓存设置便捷函数"""
    return get_redis_client().set(key, value, ex=ex)


def cache_get(key: str, default: Any = None) -> Any:
    """缓存获取便捷函数"""
    return get_redis_client().get(key, default)


def cache_delete(*keys: str) -> int:
    """缓存删除便捷函数"""
    return get_redis_client().delete(*keys)


def cache_scan(cursor: int = 0, match: Optional[str] = None, count: Optional[int] = None) -> tuple[int, List[str]]:
    """缓存扫描便捷函数"""
    return get_redis_client().scan(cursor, match, count)


def cache_scan_iter(match: Optional[str] = None, count: Optional[int] = None):
    """缓存扫描迭代器便捷函数"""
    return get_redis_client().scan_iter(match, count)


if __name__ == "__main__":
    # 测试Redis客户端
    try:
        logger.info("开始测试Redis客户端...")

        # 测试连接
        if test_connection():
            logger.info("Redis连接测试成功")

            client = get_redis_client()

            # 测试基本操作
            test_key = "test:redis:client"
            test_value = {"message": "Hello Redis!", "timestamp": "2025-08-01"}

            # 设置值
            client.set(test_key, test_value, ex=60)
            logger.info("设置测试值成功")

            # 获取值
            retrieved_value = client.get(test_key)
            logger.info("获取测试值: %s", retrieved_value)

            # 测试哈希操作
            hash_key = "test:hash:client"
            hash_data = {
                "field1": "value1",
                "field2": {"nested": "data"},
                "field3": 123,
            }

            client.hset(hash_key, hash_data)
            logger.info("设置哈希数据成功")

            # 获取哈希数据
            retrieved_hash = client.hgetall(hash_key)
            logger.info("获取哈希数据: %s", retrieved_hash)

            # 测试scan操作
            scan_test_keys = ["test:scan:key1", "test:scan:key2", "test:scan:other"]
            for key in scan_test_keys:
                client.set(key, f"value_{key}", ex=60)
            logger.info("设置scan测试数据成功")

            # 测试scan方法
            cursor, keys = client.scan(0, match="test:scan:key*", count=10)
            logger.info("scan测试结果 - cursor: %d, keys: %s", cursor, keys)

            # 测试scan_iter方法
            scan_iter_keys = list(client.scan_iter(match="test:scan:*", count=10))
            logger.info("scan_iter测试结果 - keys: %s", scan_iter_keys)

            # 清理测试数据
            client.delete(test_key, hash_key, *scan_test_keys)
            logger.info("清理测试数据完成")

            logger.info("Redis客户端测试完成")
        else:
            logger.error("Redis连接测试失败")

    except Exception as e:
        logger.error("Redis客户端测试异常: %s", e)
