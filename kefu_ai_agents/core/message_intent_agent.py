from typing import List, Optional
from kefu_ai_agents.api.schemas.session_schemas import QaMessageIntent
from kefu_ai_agents.integration.apolloy import get_config_dict
from kefu_ai_agents.integration.youling import chat_completions


def generate_message_intent(
    session_content: Optional[str], session_item_name: Optional[str], current_question: str
) -> QaMessageIntent:
    """获取消息意图

    Args:
        message: 用户输入的消息

    Returns:
        消息意图
    """

    # 获取配置
    local_test_config = {
        "user_prompt_template": "用户咨询客服会话上文：{session_content}\n咨询商品：{session_item_name}\n用户当前提问：{current_question}\n用户的真实意图是（直接输出意图，不要回复其他与意图不相关内容）"
    }
    agent_config = get_config_dict(
        "qa_message_intent_agent",
        default=local_test_config,
    )
    if not agent_config:
        raise ValueError("消息意图识别Agent配置未找到")
    system_prompt = agent_config.get("system_prompt", None)
    user_prompt_template = agent_config.get("user_prompt_template")
    if not user_prompt_template:
        raise ValueError("用户提示词未配置")

    user_prompt = user_prompt_template.format(
        session_content=session_content or "",
        session_item_name=session_item_name or "",
        current_question=current_question,
    )

    # 模型参数
    model_param = agent_config.get("model_param", {})

    # 调用大模型
    intent = chat_completions(user_prompt, system_prompt=system_prompt, **model_param)
    if not intent or intent.strip() == "":
        raise ValueError("获取消息意图失败")
    intent = intent.strip()
    if intent == "无效消息":
        return QaMessageIntent(intent=None, valid_message=False)
    else:
        return QaMessageIntent(intent=intent, valid_message=True)


if __name__ == "__main__":
    # 测试代码
    session_content = "我想退货"
    session_item_name = "网易严选全价猫粮"
    current_question = "如何退货？"

    intent = generate_message_intent(
        session_content, session_item_name, current_question
    )
    print(intent.model_dump_json())
