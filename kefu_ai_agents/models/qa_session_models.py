from typing import List, Optional
from pydantic import BaseModel, Field


class QACardMessageItem(BaseModel):
    itemId: Optional[str] = Field(None, description="商品ID")
    skuId: Optional[str] = Field(None, description="SKU ID")


class QACardMessageOrder(BaseModel):
    orderId: Optional[str] = Field(None, description="订单ID")


class QaCardMessage(BaseModel):
    itemList: Optional[List[QACardMessageItem]] = Field(
        None, description="商品卡片列表"
    )
    orderList: Optional[List[QACardMessageOrder]] = Field(
        None, description="订单卡片列表"
    )
