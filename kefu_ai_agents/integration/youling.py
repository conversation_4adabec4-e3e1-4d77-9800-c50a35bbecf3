import hashlib
import os
import random
import string
import time
from PIL import Image
from typing import List, Union
from typing import Optional, Dict, Any
from loadimg import load_img
import requests
from dotenv import load_dotenv
from kefu_ai_agents.utils.image_utils import compress_image, image_to_base64
from kefu_ai_agents.utils.loguru_config import get_logger

logger = get_logger(__name__)

load_dotenv()


def generate_nonce(length: int = 10) -> str:
    return "".join(random.choices(string.ascii_letters + string.digits, k=length))


def signed_headers(app_id: str, app_key: str) -> Dict[str, str]:
    # 生成随机字符串
    nonce = generate_nonce(10)
    # 获取当前时间戳（秒）
    timestamp = str(int(time.time()))
    # 拼接待签名的字符串
    str_to_sign = f"appId={app_id}&nonce={nonce}&timestamp={timestamp}&appkey={app_key}"
    # 生成 MD5 签名，并转换为大写
    sign = hashlib.md5(str_to_sign.encode("utf-8")).hexdigest().upper()

    headers = {
        "appId": app_id,
        "nonce": nonce,
        "timestamp": timestamp,
        "sign": sign,
        "version": "v2",
    }
    return headers


def chat_completions(
    prompt: str,
    system_prompt: Optional[str] = None,
    images: Optional[Union[List[str], List[Image.Image]]] = None,
    model: str = "gpt-4.1",
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
    top_p: Optional[float] = None,
    presence_penalty: Optional[float] = None,
    frequency_penalty: Optional[float] = None,
    timeout: int = 120,
) -> Optional[str]:
    """
    简化的聊天完成函数，支持文本和图片输入

    Args:
        prompt: 用户提示文本
        system_prompt: 可选的系统提示
        images: 可选的图片列表，可以是文件路径字符串或PIL Image对象
        model: 使用的模型名称，详见 https://docs.popo.netease.com/lingxi/79d20d571dfd453aa25fafc7885cac4d
        temperature: 控制响应随机性
        max_tokens: 生成的最大token数量
    Returns:
        助手的回复文本，如果请求失败则返回空字符串
    """

    messages = []
    # 添加系统消息（如果提供）
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})

    # 构建用户消息
    user_content = []
    user_content.append({"type": "text", "text": prompt})

    # claude模型max_tokens必填
    if model.startswith("claude"):
        max_tokens = max_tokens or 10000

    # 添加图片内容（如果提供）
    if images:
        for image in images:
            image = load_img(image)
            image = compress_image(image, max_size_mb=2.5)

            if model.startswith("claude"):
                mime, b64 = image_to_base64(image, mime_type_prefix=False)
                msg_img = {
                    "type": "image",
                    "source": {
                        "type": "base64",
                        "media_type": mime,
                        "data": b64,
                    },
                }
            else:
                mime_type, base64_data = image_to_base64(image, mime_type_prefix=True)
                msg_img = {
                    "type": "image_url",
                    "image_url": {
                        "url": base64_data,
                    },
                }
            user_content.append(msg_img)

    # 添加用户消息
    messages.append({"role": "user", "content": user_content})
    # 调用chat_completions函数
    response = send_chat_request(
        messages,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
        presence_penalty=presence_penalty,
        frequency_penalty=frequency_penalty,
        timeout=timeout,
    )
    if not response:
        return None

    try:
        detail = response.get("detail") or {}
        choices = detail.get("choices") or []

        if choices and isinstance(choices[0], dict):
            message = choices[0].get("message") or {}
            return message.get("content")

        return None
    except Exception as e:
        logger.exception(f"解析chat响应出错: {e}, response: {response}")
    return None


def send_chat_request(
    messages: List[Dict[str, str]],
    model: str = "gpt-4.1",
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
    top_p: Optional[float] = None,
    presence_penalty: Optional[float] = None,
    frequency_penalty: Optional[float] = None,
    timeout: int = 120,
) -> Optional[Dict[str, Any]]:
    """
    调用 chat 接口，传入 API 所需参数

    参数:
        messages (List[Dict[str, str]]): 消息记录列表，每条记录包含 role 与 content
        model (str): 模型，比如 "gpt-3.5-turbo"
        max_tokens (int): 返回最大 token 数
        temperature (float): 温度参数
        top_p (Union[int, float]): top_p 参数
        stop (Optional[Union[str, List[str]]]): 停止生成的标记
        presence_penalty (Union[int, float]): 出现惩罚参数
        frequency_penalty (Union[int, float]): 频率惩罚参数
        timeout (int): 请求超时时间，单位秒

    返回:
        Optional[Dict[str, Any]]: 接口返回的 JSON 数据，或在异常情况返回 None
    """
    base_path: str = "http://aigc-api.apps-hangyan.danlu.netease.com"
    url: str = f"{base_path}/api/v2/text/chat"

    app_id = os.getenv("APP_ID", "")
    app_key = os.getenv("APP_KEY", "")
    headers: Dict[str, str] = signed_headers(app_id, app_key)
    headers["Content-Type"] = "application/json"

    payload = {
        "messages": messages,
        "model": model,
    }
    if temperature:
        payload["temperature"] = temperature
    if max_tokens:
        payload["maxTokens"] = max_tokens
    if top_p:
        payload["topP"] = top_p
    if presence_penalty:
        payload["presencePenalty"] = presence_penalty
    if frequency_penalty:
        payload["frequencyPenalty"] = frequency_penalty

    response = None
    start_time = time.time()
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=timeout)
        elapsed = time.time() - start_time
        response.raise_for_status()  # 检查 HTTP 错误
        logger.info(
            "请求响应: status_code:{} headers:{} text:{} 处理时间:{:.3f}s",
            response.status_code,
            response.headers,
            response.text,
            elapsed,
        )
        return response.json()  # 返回 JSON 格式数据
    except Exception as e:
        elapsed = time.time() - start_time
        exception_info = (
            "响应为空"
            if response is None
            else f"请求响应: status_code:{response.status_code}, headers:{response.headers}, text:{response.text}"
        )
        logger.exception(
            "{},请求异常: {}, 处理时间:{:.3f}s", exception_info, e, elapsed
        )
        return None


if __name__ == "__main__":
    res = chat_completions(
        "你好，今天的天气怎么样？",
        system_prompt="你是一个天气助手。",
        model="gpt-4.1",
        temperature=0.2,
        max_tokens=100,
        top_p=0.8,
        timeout=30,
    )
    print(res)  # 输出助手的回复文本
