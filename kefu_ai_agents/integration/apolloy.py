import json
import os
import threading
from typing import Dict, List, Optional, Any
from contextlib import contextmanager

from yxapollo import Apollo
from kefu_ai_agents.utils.loguru_config import get_logger
from dotenv import load_dotenv

logger = get_logger(__name__)

load_dotenv()


class ApolloClientError(Exception):
    """Apollo客户端异常类"""

    pass


class ApolloClient:
    """Apollo配置客户端单例类

    线程安全的单例实现，用于管理Apollo配置中心的连接和配置获取
    """

    _instance: Optional["ApolloClient"] = None
    _lock = threading.Lock()

    def __new__(cls) -> "ApolloClient":
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    instance = super().__new__(cls)
                    instance._initialize()
                    cls._instance = instance
        return cls._instance

    def _initialize(self) -> None:
        """初始化方法，只会被调用一次"""
        self._apollo: Optional[Apollo] = None
        self._app_id: str = ""
        self._service_host: str = ""
        self._is_initialized: bool = False
        self._init_apollo_client()

    def _init_apollo_client(self) -> None:
        """初始化Apollo客户端"""
        try:
            self._app_id = self._get_required_env("APOLLO_APP_ID")
            self._service_host = self._get_required_env("APOLLO_SERVICE_HOST")

            self._apollo = Apollo(
                appid=self._app_id, config_server_url=self._service_host
            )

            self._is_initialized = True
            logger.info(
                "[ApolloClient] 初始化成功 - app_id: %s, config_server_url: %s",
                self._app_id,
                self._service_host,
            )

        except Exception as e:
            error_msg = f"Apollo客户端初始化失败 - app_id: {self._app_id}, host: {self._service_host}, error: {str(e)}"
            logger.error(error_msg)
            raise ApolloClientError(error_msg) from e

    @staticmethod
    def _get_required_env(key: str) -> str:
        """获取必需的环境变量

        Args:
            key: 环境变量键名

        Returns:
            环境变量值

        Raises:
            ValueError: 当环境变量不存在或为空时
        """
        val = os.environ.get(key, "").strip()
        if not val:
            raise ValueError(f"缺少必需的环境变量: {key}")
        return val

    @property
    def client(self) -> Apollo:
        """获取Apollo客户端实例

        Returns:
            Apollo客户端实例

        Raises:
            ApolloClientError: 当客户端未初始化时
        """
        if not self._is_initialized or self._apollo is None:
            raise ApolloClientError("Apollo客户端未正确初始化")
        return self._apollo

    def get_value(
        self, key: str, namespace: str = "application", default: Optional[str] = None
    ) -> Optional[str]:
        """获取配置值

        Args:
            key: 配置键，不能为空
            namespace: 命名空间，默认为 application
            default: 默认值，当配置不存在时返回

        Returns:
            配置值

        Raises:
            ApolloClientError: 获取配置失败时抛出
            ValueError: 当key为空时抛出
        """
        if not key or not key.strip():
            raise ValueError("配置键不能为空")

        try:
            result = self.client.get_value(key, namespace, default or "")
            logger.debug(
                "[ApolloClient] 获取配置成功 - key: %s, namespace: %s, value: %s",
                key,
                namespace,
                result,
            )
            return result

        except Exception as e:
            error_msg = (
                f"获取Apollo配置失败 - key: {key}, namespace: {namespace}, "
                f"app_id: {self._app_id}, error: {str(e)}"
            )
            logger.error(error_msg)
            raise ApolloClientError(error_msg) from e

    @contextmanager
    def error_context(self, operation: str):
        """错误上下文管理器"""
        try:
            yield
        except ApolloClientError:
            raise
        except Exception as e:
            error_msg = f"{operation}失败: {str(e)}"
            logger.error(error_msg)
            raise ApolloClientError(error_msg) from e


# 全局访问函数
def get_apollo_client() -> ApolloClient:
    """获取Apollo客户端单例实例

    Returns:
        ApolloClient实例
    """
    return ApolloClient()


def get_value_with_type(
    key: str,
    value_type: type,
    namespace: str = "application",
    default: Any = None,
) -> Any:
    """获取指定类型的配置值

    Args:
        key: 配置键
        value_type: 目标类型 (int, float, bool, str)
        namespace: 命名空间
        default: 默认值

    Returns:
        转换后的配置值

    Raises:
        ApolloClientError: 获取或转换失败时抛出
    """
    raw_value = get_config_value(
        key, namespace, str(default) if default is not None else None
    )
    if raw_value is None:
        return default

    try:
        if value_type == bool:
            return raw_value.lower() in ("true", "1", "yes", "on")
        elif value_type in (int, float):
            return value_type(raw_value)
        else:
            return raw_value

    except (ValueError, TypeError) as e:
        error_msg = f"配置值类型转换失败 - key: {key}, value: {raw_value}, target_type: {value_type.__name__}"
        logger.exception(error_msg)
        raise ApolloClientError(error_msg) from e


def get_config_value(
    key: str, namespace: str = "application", default: Optional[str] = None
) -> Optional[str]:
    """便捷函数：获取单个配置值

    Args:
        key: 配置键
        namespace: 命名空间
        default: 默认值

    Returns:
        配置值
    """
    project_env = os.getenv("PROJECT_ENV")
    env_list = ["test", "online", "release", "regression"]
    if not project_env or project_env.lower() not in env_list:
        logger.info("当前环境不支持Apollo配置获取，使用默认值")
        return default

    return get_apollo_client().get_value(key, namespace, default)


def get_config_dict(
    key: str,
    namespace: str = "application",
    default: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """便捷函数：获取字典类型的JSON配置

    Args:
        key: 配置键
        namespace: 命名空间
        default: 默认值

    Returns:
        解析后的字典对象
    """
    json_str = get_config_value(key, namespace)

    if not json_str:
        return default or {}

    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        return default or {}


def get_config_list(
    key: str,
    namespace: str = "application",
    default: Optional[List[Any]] = None,
) -> List[Any]:
    """便捷函数：获取数组类型的JSON配置

    Args:
        key: 配置键
        namespace: 命名空间
        default: 默认值

    Returns:
        解析后的数组对象
    """
    json_str = get_config_value(key, namespace)

    if not json_str:
        return default or []

    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        return default or []


def get_config_int(
    key: str, namespace: str = "application", default: Optional[int] = None
) -> int:
    """便捷函数：获取整型配置值"""
    return get_value_with_type(key, int, namespace, default)


def get_config_bool(
    key: str, namespace: str = "application", default: Optional[bool] = None
) -> bool:
    """便捷函数：获取布尔型配置值"""
    return get_value_with_type(key, bool, namespace, default)


if __name__ == "__main__":
    try:

        # 测试获取不同类型的配置
        string_value = get_config_value("test_key", "application", "default_value")
        int_value = get_config_int("timeout", "application", 30)
        bool_value = get_config_bool("debug_mode", "application", False)

        print(f"字符串配置: {string_value}")
        print(f"整型配置: {int_value}")
        print(f"布尔配置: {bool_value}")

    except ApolloClientError as e:
        logger.error("Apollo配置获取失败: {}", str(e))
    except Exception as e:
        logger.error("未预期的错误: {}", str(e))
