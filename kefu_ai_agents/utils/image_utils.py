import base64
import io
from typing import Tuple, Union
from PIL import Image
from loadimg import load_img
from kefu_ai_agents.utils.loguru_config import get_logger


logger = get_logger(__name__)


def compress_image(
    image: Image.Image,
    max_size_mb: float = 4.5,
    max_dimension: int = 2048,
    quality: int = 85,
) -> Image.Image:
    """
    压缩图片以满足API大小限制，保持原格式
    如果图片大小已经符合要求，则不进行压缩

    Args:
        image: PIL Image对象
        max_size_mb: 最大文件大小（MB）
        max_dimension: 最大尺寸（宽或高的最大值）
        quality: 压缩质量（1-100）

    Returns:
        压缩后的PIL Image对象（如果需要压缩）或原图（如果已符合要求）
    """
    # 复制图片避免修改原图
    img = image.copy()
    original_format = img.format or "PNG"
    max_size_bytes = int(max_size_mb * 1024 * 1024)

    # 检查原始图片大小是否已经符合要求
    def get_image_size(img_obj: Image.Image) -> int:
        """获取图片的字节大小"""
        buffer = io.BytesIO()
        save_kwargs = {"format": original_format, "optimize": True}

        if original_format.upper() == "PNG":
            save_kwargs["compress_level"] = 9
        elif original_format.upper() in ["JPEG", "JPG"]:
            save_kwargs["quality"] = quality

        img_obj.save(buffer, **save_kwargs)
        return buffer.tell()

    # 如果图片大小和尺寸都符合要求，直接返回原图
    original_width, original_height = img.size
    is_size_ok = max(original_width, original_height) <= max_dimension
    is_filesize_ok = get_image_size(img) <= max_size_bytes

    if is_size_ok and is_filesize_ok:
        return img

    # 按比例缩放图片
    if max(original_width, original_height) > max_dimension:
        ratio = max_dimension / max(original_width, original_height)
        new_width = int(original_width * ratio)
        new_height = int(original_height * ratio)
        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

    # 根据原格式选择压缩策略
    if original_format.upper() in ["JPEG", "JPG"]:
        # JPEG格式：调整质量
        for q in range(quality, 20, -5):
            buffer = io.BytesIO()
            img.save(buffer, format="JPEG", quality=q, optimize=True)

            if buffer.tell() <= max_size_bytes:
                buffer.seek(0)
                compressed_img = Image.open(buffer)
                compressed_img.format = "JPEG"
                return compressed_img

    else:
        # PNG等其他格式：尝试优化和缩放
        buffer = io.BytesIO()
        save_kwargs = {"format": original_format, "optimize": True}

        # PNG格式添加压缩级别
        if original_format.upper() == "PNG":
            save_kwargs["compress_level"] = 9

        img.save(buffer, **save_kwargs)

        if buffer.tell() <= max_size_bytes:
            buffer.seek(0)
            compressed_img = Image.open(buffer)
            compressed_img.format = original_format
            return compressed_img

    # 如果还是太大，进一步缩小尺寸
    for scale in [0.8, 0.6, 0.4, 0.2]:
        width = int(img.size[0] * scale)
        height = int(img.size[1] * scale)
        scaled_img = img.resize((width, height), Image.Resampling.LANCZOS)

        buffer = io.BytesIO()

        if original_format.upper() in ["JPEG", "JPG"]:
            scaled_img.save(buffer, format="JPEG", quality=50, optimize=True)
        else:
            save_kwargs = {"format": original_format, "optimize": True}
            if original_format.upper() == "PNG":
                save_kwargs["compress_level"] = 9
            scaled_img.save(buffer, **save_kwargs)

        if buffer.tell() <= max_size_bytes:
            buffer.seek(0)
            compressed_img = Image.open(buffer)
            compressed_img.format = original_format
            return compressed_img

    # 最后的保险方案：保持格式但极小尺寸
    final_img = img.resize((200, 200), Image.Resampling.LANCZOS)
    final_img.format = original_format
    return final_img


def image_to_base64(
    image_input: Union[str, Image.Image], mime_type_prefix: bool = False
) -> Tuple[str, str]:
    """
    支持路径或PIL.Image.Image对象，转base64

    Args:
        image_input: 图像文件路径或PIL.Image.Image对象
        mime_type_prefix: 是否添加MIME类型前缀

    Returns:
        base64编码字符串
    """
    try:
        # 如果是字符串路径，加载图像
        if isinstance(image_input, str):
            image = load_img(image_input)
        elif isinstance(image_input, Image.Image):
            image = image_input
        else:
            raise ValueError("输入必须是文件路径字符串或PIL.Image.Image对象")

        if image is None:
            raise ValueError("无法加载图片，请检查输入类型和内容")

        buffer = io.BytesIO()
        fmt = image.format or "PNG"
        image.save(buffer, format=fmt)
        image_bytes = buffer.getvalue()
        encoded_string = base64.b64encode(image_bytes).decode("utf-8")
        mime_type = f"image/{fmt.lower()}"

        if mime_type_prefix:
            return mime_type, f"data:{mime_type};base64,{encoded_string}"
        return mime_type, encoded_string

    except Exception as e:
        logger.exception(f"图像转base64失败: {e}")
        raise
