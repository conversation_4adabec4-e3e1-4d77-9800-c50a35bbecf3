import os
from typing import List, Optional
import uuid
from kefu_ai_agents.api.schemas.session_schemas import (
    QaMessage,
    QaMessageContentType,
    QaMessageIntent,
    QaMessageIntentRequest,
    QaMessageSource,
)
from kefu_ai_agents.core.message_intent_agent import generate_message_intent
from kefu_ai_agents.db.redis_client import cache_set
from kefu_ai_agents.integration.apolloy import get_config_dict
from kefu_ai_agents.models.qa_session_models import QaCardMessage
import json

from kefu_ai_agents.service.channel_item_service import (
    ChannelItemRelationRequest,
    get_channel_item_relation,
)
from kefu_ai_agents.utils.loguru_config import get_logger

logger = get_logger(__name__)


def get_message_intent(message: QaMessageIntentRequest) -> QaMessageIntent:
    """获取消息意图

    Args:
        message: 用户输入的消息

    Returns:
        消息意图
    """

    # 测试环境 mock
    if os.getenv("PROJECT_ENV", "").lower() == "test":
        mock_qa_message_intent = get_config_dict("mock_qa_message_intent")
        if (
            mock_qa_message_intent
            and mock_qa_message_intent.get("enable", False)
            and mock_qa_message_intent.get("data")
        ):
            return QaMessageIntent(**mock_qa_message_intent.get("data", {}))

    # 过滤消息，只处理用户发送的文本消息
    curr_msg = message.current_message
    if (
        curr_msg.source != QaMessageSource.CUSTOMER
        or curr_msg.content_type != QaMessageContentType.TEXT
    ):
        return QaMessageIntent(intent=None, valid_message=False)

    # 倒序获取商品卡片消息
    item_card_msg = None
    context_messages = message.session_context
    if context_messages:
        context_messages = context_messages[::-1]  # 反转处理
        for msg in context_messages:
            if (
                msg.content_type == QaMessageContentType.GOOD_CARD
                and msg.source == QaMessageSource.CUSTOMER
            ):
                item_card_msg = msg
                break

    # 获取商品名称
    item_name = None
    if item_card_msg:
        # 解析商品卡片消息
        card_message = parse_card_message(item_card_msg.content)
        if card_message.itemList:
            item_id = card_message.itemList[0].itemId
            sku_id = card_message.itemList[0].skuId
            # 获取渠道商品关联信息
            req = ChannelItemRelationRequest(
                channel_name=None,
                org_channel_id=None,
                item_id=None,
                sku_id=None,
                out_item_id=[item_id] if item_id else None,
                out_sku_id=[sku_id] if sku_id else None,
            )
            items = get_channel_item_relation(req)

            # 如果存在渠道商品信息，则获取非空商品名称
            if items:
                item_name = next(
                    (item.out_item_name for item in items if item.out_item_name),
                    None,
                )
    # 格式化会话上下文
    session_str = format_session_context(message.session_context)
    # 生成消息意图
    msg_intent = generate_message_intent(session_str, item_name, curr_msg.content)

    # 会话上下文和结果存入redis
    save_intent_context(message, msg_intent)

    return msg_intent


def save_intent_context(message: QaMessageIntentRequest, intent: QaMessageIntent):
    """会话上下文和结果存入redis

    Args:
        message: 会话上下文
        intent: 消息意图
    """
    if not message or not intent or not intent.valid_message:
        return

    try:
        # 随机消息id
        message_uuid = uuid.uuid4().hex
        key = f"qa_message_intent_context:{message.session_id}:{message_uuid}"

        context = message.model_dump(exclude_none=True)
        context["intent"] = intent.intent

        ex = 60 * 60 * 24 * 2
        success = cache_set(key, context, ex)
        if not success:
            raise ValueError("cache_set failed")
    except Exception as e:
        logger.error(
            "[save_intent_context] failed, error:{}, message:{}, intent:{}",
            str(e),
            message.model_dump_json(),
            intent.model_dump_json(),
            exc_info=True,
        )


def format_session_context(session_context: Optional[List[QaMessage]]) -> str:
    """格式化会话上下文为字符串

    Args:
        session_context: 会话上下文列表

    Returns:
        格式化后的字符串
    """
    if not session_context:
        return ""
    msg = ""
    for message in session_context:
        if not message.content:
            continue

        content = message.content
        if message.content_type == QaMessageContentType.IMAGE:
            content = f"[图片]{content}"
        elif message.content_type == QaMessageContentType.ORDER_CARD:
            content = f"[订单卡片]{content}"
        elif message.content_type == QaMessageContentType.GOOD_CARD:
            content = f"[商品卡片]{content}"
        elif message.content_type == QaMessageContentType.AFTER_SALE_CARD:
            content = f"[售后卡片]{content}"
        elif message.content_type == QaMessageContentType.FILE:
            content = f"[文件]{content}"
        elif message.content_type == QaMessageContentType.LINK:
            content = f"[链接]{content}"
        elif message.content_type == QaMessageContentType.AUDIO:
            content = f"[音频]{content}"
        elif message.content_type == QaMessageContentType.VIDEO:
            content = f"[视频]{content}"

        if message.source == QaMessageSource.CUSTOMER:
            msg += f"用户: {content}\n"
        elif message.source == QaMessageSource.STAFF:
            msg += f"客服: {content}\n"
        elif message.source == QaMessageSource.ROBOT:
            msg += f"机器人: {content}\n"
        elif message.source == QaMessageSource.SYSTEM:
            msg += f"系统: {content}\n"

    return msg


def parse_card_message(card_msg: str) -> QaCardMessage:
    """解析商品卡片消息

    Args:
        card_msg: JSON字符串格式的卡片消息

    Returns:
        解析后的QaCardMessage模型实例

    Raises:
        ValueError: 当JSON解析失败时抛出
        ValidationError: 当数据验证失败时抛出
    """
    try:
        # 解析JSON字符串
        card_data = json.loads(card_msg)

        # 使用Pydantic的model_validate方法解析嵌套模型
        card_message = QaCardMessage.model_validate(card_data)

        return card_message

    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON format: {str(e)}")
    except Exception as e:
        raise ValueError(f"Failed to parse card message: {str(e)}")


if __name__ == "__main__":
    # JSON 字符串示例
    json_str = """
    {
        "itemList": [
            {
                "itemId": "123456",
                "skuId": "sku_001"
            },
            {
                "itemId": "789012"
            }
        ]
    }
    """

    # 解析为模型实例
    card_message = parse_card_message(json_str)

    # 访问嵌套数据
    print(card_message.model_dump_json(indent=2, exclude_none=True))
