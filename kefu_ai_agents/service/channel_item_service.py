import os
from typing import List, Optional
from pydantic import BaseModel, Field

from kefu_ai_agents.integration.apolloy import get_config_list, get_config_value
from kefu_ai_agents.integration.waiter import query_waiter


class ChannelItemRelationRequest(BaseModel):
    """请求参数模型"""

    channel_name: Optional[List[str]] = Field(None, description="渠道名")
    org_channel_id: Optional[List[str]] = Field(None, description="渠道id")
    sku_id: Optional[List[int]] = Field(None, description="严选SkuId")
    item_id: Optional[List[int]] = Field(None, description="严选ItemId")
    out_item_id: Optional[List[str]] = Field(None, description="渠道商品ID")
    out_sku_id: Optional[List[str]] = Field(None, description="渠道商品SKU ID")


class ChannelItemRelation(BaseModel):
    """渠道商品关联信息模型"""

    channel_tag: Optional[int] = Field(None, description="分销库渠道自增Id")
    org_channel_id: Optional[str] = Field(None, description="渠道id")
    channel_name: Optional[str] = Field(None, description="渠道名")
    sku_id: Optional[int] = Field(None, description="严选SkuId")
    item_id: Optional[int] = Field(None, description="严选ItemId")
    out_sku_id: Optional[str] = Field(None, description="渠道SkuId")
    out_item_id: Optional[str] = Field(None, description="渠道ItemId")
    out_item_name: Optional[str] = Field(None, description="外渠商品名称")
    out_item_pic_url: Optional[str] = Field(None, description="外渠商品主图url")
    sku_status: Optional[str] = Field(
        None,
        description="外渠sku当天的上下架状态，删除：-1，上架：1，下架：2",
    )


def get_channel_item_relation(request: ChannelItemRelationRequest) -> List[ChannelItemRelation]:
    """获取渠道商品关联信息

    Args:
        request: 渠道商品关联请求参数

    Returns:
        渠道商品关联信息
    """
    if os.getenv("PROJECT_ENV", "").lower() == "test":
        mock = get_config_list("mock_waiter_data_service_url_dm_yx_goods_fx_sku_relation_l1", default=[])
        return [ChannelItemRelation(**item) for item in mock]

    waiter_data_service_url = get_config_value("waiter_data_service_url_dm_yx_goods_fx_sku_relation_l1")
    if not waiter_data_service_url:
        raise ValueError("waiter_data_service_url_dm_yx_goods_fx_sku_relation_l1 is not configured")
    
    result = query_waiter(waiter_data_service_url, request.model_dump())

    if not result or not result.get_data():
        return []

    return [ChannelItemRelation(**item) for item in result.get_data()]
