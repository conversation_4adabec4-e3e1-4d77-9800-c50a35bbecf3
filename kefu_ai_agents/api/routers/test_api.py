from fastapi import APIRouter
from kefu_ai_agents.api.schemas.common_schemas import ApiResponse

# 创建路由器实例
router = APIRouter(prefix="/test", tags=["test"])


@router.get("", response_model=ApiResponse)
async def test_hello():
    """测试接口 - Hello World"""
    return ApiResponse(
        code=200,
        message="测试接口调用成功",
        data={"message": "Hello from test router!", "endpoint": "/test"},
    )


@router.get("/exception", response_model=ApiResponse)
async def test_exception():
    """测试接口 - 异常处理"""
    # 这里故意抛出一个异常
    n = 1 / 0
