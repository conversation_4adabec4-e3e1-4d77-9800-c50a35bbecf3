from enum import IntEnum
from typing import List, Optional
from pydantic import BaseModel, ConfigDict, Field, alias_generators


class QaMessageContentType(IntEnum):
    """消息内容类型枚举"""

    OTHER = 0  # 其他类型
    TEXT = 1  # 文本
    IMAGE = 2  # 图片
    ORDER_CARD = 3  # 订单卡片
    GOOD_CARD = 4  # 商品卡片
    AFTER_SALE_CARD = 5  # 售后卡片类型
    SYSTEM = 6  # 系统消息
    EMOJI = 7  # 表情
    FILE = 8  # 文件
    LINK = 9  # 链接
    AUDIO = 10  # 音频
    VIDEO = 11  # 视频


class QaMessageSource(IntEnum):
    """消息来源角色枚举"""

    CUSTOMER = 1  # 客户
    STAFF = 2  # 客服
    SYSTEM = 3  # 系统
    ROBOT = 4  # 机器人


class QaMessage(BaseModel):
    """问答消息实体类型"""

    content_type: QaMessageContentType = Field(..., description="消息内容类型")
    source: QaMessageSource = Field(..., description="消息来源角色")
    content: str = Field(..., description="消息内容")

    model_config = ConfigDict(
        alias_generator=alias_generators.to_camel,
        populate_by_name=True,
    )


class QaMessageIntent(BaseModel):
    """问答消息意图实体类型"""

    intent: Optional[str] = Field(None, description="意图描述")
    valid_message: bool = Field(..., description="当前消息是否为有效问题")

    model_config = ConfigDict(
        alias_generator=alias_generators.to_camel,
        populate_by_name=True,
    )


class QaMessageIntentRequest(BaseModel):
    """请求参数模型"""

    session_id: str = Field(..., description="会话ID")
    session_context: Optional[List[QaMessage]] = Field(None, description="会话上文")
    current_message: QaMessage = Field(..., description="当前消息")

    model_config = ConfigDict(
        alias_generator=alias_generators.to_camel,
        populate_by_name=True,
    )
