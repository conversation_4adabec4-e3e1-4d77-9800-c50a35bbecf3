# -- coding: utf-8 --
from fastapi import FastAPI
from http import HTTPStatus
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import uvicorn
from dotenv import load_dotenv

from kefu_ai_agents.api.schemas.common_schemas import ApiResponse
from kefu_ai_agents.api.routers import test_api, qa_api
from kefu_ai_agents.utils.loguru_config import get_logger

# 加载环境变量
load_dotenv()
# 获取应用日志记录器
logger = get_logger(__name__)

# 创建 FastAPI 应用实例
app = FastAPI(
    title="客服 AI Agents 服务 API",
    description="客服 AI Agents 服务 API",
    version="1.0.0",
)

# 应用启动时记录日志
logger.info("FastAPI 应用已创建，日志系统正常工作")

# 注册路由
app.include_router(test_api.router)
app.include_router(qa_api.router)


# 健康检查接口
@app.get("/i/health", response_model=ApiResponse)
async def health_check():
    """健康检查接口"""
    return ApiResponse(
        code=HTTPStatus.OK,
        message="服务运行正常",
        data={"status": "healthy", "service": "kefu-ai-agents"},
    )


# 根路径接口
@app.get("/", response_model=ApiResponse)
async def root():
    """根路径接口"""
    return ApiResponse(
        code=HTTPStatus.OK,
        message="欢迎使用客服 AI Agents 服务 API",
        data={"version": "1.0.0", "documentation": "请访问 /docs 查看 API 文档"},
    )


# Pydantic 验证异常处理器
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc: RequestValidationError):
    """处理 Pydantic 参数校验异常"""
    logger.warning("request validation error: {}", exc)
    api_response = ApiResponse(
        code=HTTPStatus.UNPROCESSABLE_ENTITY,
        message=f"request validation error: {str(exc)}",
        data=None,
    )
    return JSONResponse(
        status_code=HTTPStatus.UNPROCESSABLE_ENTITY,
        content=api_response.model_dump(),
    )


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"internal server error: {str(exc)}")
    api_response = ApiResponse(
        code=HTTPStatus.INTERNAL_SERVER_ERROR,
        message=f"internal server error: {str(exc)}",
        data=None,
    )
    return JSONResponse(
        status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        content=api_response.model_dump(),
    )


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=True)
