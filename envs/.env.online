# Redis
REDIS_CLUSTER_NAME=yx_kefu_ai_agents_ga_onl
REDIS_CLUSTER_NODES=***********:16409,***********:16409,***********:16409,***********:16409,***********:16409,***********:16409
REDIS_PASSWORD=lOXmSs2DWJUU
REDIS_PREFIX=kefu_ai_agents:

# youling
APP_ID=cf45284b-3719-47b0-9a59-a0e1c2bd1c96
APP_KEY=clq3phur71pfuma43rqi9isg8b0f6e

# ApolloY
APOLLO_APP_ID=kefu-ai-agents
APOLLO_SERVICE_HOST=http://127.0.0.1:8550/proxy/online.apolloy-configservice.service.mailsaas


# 基础日志配置
LOG_LEVEL=INFO
LOG_FILE=/home/<USER>/kefu-ai-agents/app.log
LOG_TO_CONSOLE=False
LOG_MAX_SIZE=50 MB
LOG_BACKUP_COUNT=5
LOG_ROTATE_WHEN=00:00
