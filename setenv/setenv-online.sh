#!/bin/sh

PROJECT_ENV="online"
PORT=8080
INSTALL_DEPENDENCE=true


install_cmd="/home/<USER>/bin/pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple"

start_cmd="/home/<USER>/packages/python3.12.4/bin/gunicorn kefu_ai_agents.api.main:app --bind 0.0.0.0:$PORT --workers 8 --worker-class uvicorn.workers.UvicornWorker --log-level info --timeout 1200 --access-logfile /home/<USER>/kefu-ai-agents/access.log --error-logfile /home/<USER>/kefu-ai-agents/error.log --capture-output"

consul_service_tags=$PROJECT_ENV
consul_service_port=$PORT
consul_health_url="/i/health"

# 暴露指定变量，平台部署时使用
export PROJECT_ENV
export INSTALL_DEPENDENCE

